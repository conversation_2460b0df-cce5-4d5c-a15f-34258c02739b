import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import AdminLayout from '@/components/admin/AdminLayout'
import ProtectedRoute from '@/components/admin/ProtectedRoute'
import UserList from '@/components/admin/users/UserList'
import UserManagementDashboard from '@/components/admin/users/UserManagementDashboard'
import ApplicationsList from '@/components/admin/users/ApplicationsList'
import ApplicationReviewModal from '@/components/admin/users/ApplicationReviewModal'
import { useAuth } from '@/contexts/AuthContext'
import { safeRender } from '@/lib/safe-render-utils'
import styles from '@/styles/admin/UsersPage.module.css'

export default function UsersPage() {
  const router = useRouter()
  const { hasAdminAccess } = useAuth()
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [activeTab, setActiveTab] = useState('dashboard')

  // Handle URL fragment for direct tab navigation
  useEffect(() => {
    if (typeof window !== 'undefined') {
      const hash = window.location.hash.replace('#', '')
      if (hash && ['dashboard', 'users', 'applications'].includes(hash)) {
        setActiveTab(hash)
      }
    }
  }, [])

  // Applications state
  const [applications, setApplications] = useState([])
  const [selectedApplication, setSelectedApplication] = useState(null)
  const [showReviewModal, setShowReviewModal] = useState(false)
  const [applicationsLoading, setApplicationsLoading] = useState(false)
  const [applicationsError, setApplicationsError] = useState(null)
  const [filters, setFilters] = useState({
    status: 'all',
    type: 'all',
    search: ''
  })
  const [stats, setStats] = useState({
    pending: 0,
    under_review: 0,
    approved: 0,
    rejected: 0,
    total: 0
  })

  // Fetch applications when applications tab is active
  useEffect(() => {
    if (activeTab === 'applications') {
      fetchApplications()
    }
  }, [activeTab, filters])

  const fetchApplications = async () => {
    try {
      setApplicationsLoading(true)
      setApplicationsError(null)

      const params = new URLSearchParams({
        status: filters.status,
        type: filters.type,
        search: filters.search,
        limit: '50',
        offset: '0'
      })

      const response = await fetch(`/api/admin/users/applications?${params}`)

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      const data = await response.json()
      setApplications(data.applications || [])
      setStats(data.stats || {})
    } catch (error) {
      console.error('❌ Error fetching applications:', error)
      setApplicationsError(error.message)
    } finally {
      setApplicationsLoading(false)
    }
  }

  const handleReviewApplication = (application) => {
    setSelectedApplication(application)
    setShowReviewModal(true)
  }

  const handleReviewSubmit = async (applicationId, status, notes) => {
    try {
      const response = await fetch(`/api/admin/users/applications/${applicationId}/review`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ status, notes }),
      })

      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // Refresh applications list
      await fetchApplications()

      // Close modal
      setShowReviewModal(false)
      setSelectedApplication(null)

      alert(`Application ${status === 'approved' ? 'approved' : 'rejected'} successfully!`)
    } catch (error) {
      console.error('❌ Error submitting review:', error)
      alert(`Error submitting review: ${error.message}`)
    }
  }

  const handleFilterChange = (newFilters) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
  }

  // Handle new user button click
  const handleNewUser = () => {
    router.push('/admin/users/new')
  }

  const tabs = [
    { id: 'dashboard', label: 'Dashboard', icon: '📊' },
    { id: 'users', label: 'All Users', icon: '👥' },
    { id: 'applications', label: 'Applications', icon: '📝' }
  ]

  return (
    <ProtectedRoute adminOnly>
      <AdminLayout title="User Management">
        <div className={styles.usersPage}>
          <div className={styles.header}>
            <h2>User Management</h2>
            <div className={styles.actions}>
              <button
                className={styles.addButton}
                onClick={handleNewUser}
              >
                <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
                  <path d="M12 5v14M5 12h14"></path>
                </svg>
                Add New User
              </button>
            </div>
          </div>

          {error && (
            <div className={styles.error}>
              {error}
            </div>
          )}

          {/* Tab Navigation */}
          <div className={styles.tabNavigation}>
            {tabs.map(tab => (
              <button
                key={tab.id}
                className={`${styles.tab} ${activeTab === tab.id ? styles.activeTab : ''}`}
                onClick={() => setActiveTab(tab.id)}
              >
                <span className={styles.tabIcon}>{tab.icon}</span>
                {tab.label}
              </button>
            ))}
          </div>

          {/* Tab Content */}
          <div className={styles.tabContent}>
            {activeTab === 'dashboard' && <UserManagementDashboard />}
            {activeTab === 'users' && <UserList />}
            {activeTab === 'applications' && (
              <div className={styles.applicationsSection}>
                {applicationsError && (
                  <div className={styles.error}>
                    {safeRender(applicationsError, 'Error loading applications')}
                  </div>
                )}

                <ApplicationsList
                  applications={applications}
                  loading={applicationsLoading}
                  error={applicationsError}
                  stats={stats}
                  filters={filters}
                  onFilterChange={handleFilterChange}
                  onReviewApplication={handleReviewApplication}
                />
              </div>
            )}
          </div>
        </div>

        {/* Application Review Modal */}
        {showReviewModal && selectedApplication && (
          <ApplicationReviewModal
            application={selectedApplication}
            onClose={() => {
              setShowReviewModal(false)
              setSelectedApplication(null)
            }}
            onSubmit={handleReviewSubmit}
          />
        )}
      </AdminLayout>
    </ProtectedRoute>
  )
}
