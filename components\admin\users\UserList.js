import { useState, useEffect } from 'react'
import { useRouter } from 'next/router'
import Link from 'next/link'
import userManagement from '@/lib/user-management'
import ConfirmationDialog from '@/components/admin/ConfirmationDialog'
import ResendWelcomeEmailButton from './ResendWelcomeEmailButton'
import TokenManagement from './TokenManagement'
import styles from '@/styles/admin/users/UserList.module.css'

export default function UserList() {
  const router = useRouter()
  const [users, setUsers] = useState([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState(null)
  const [search, setSearch] = useState('')
  const [tokenManagement, setTokenManagement] = useState(null)
  const [roleFilter, setRoleFilter] = useState('all')
  const [sortBy, setSortBy] = useState('created_at')
  const [sortOrder, setSortOrder] = useState('desc')
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(1)
  const [totalUsers, setTotalUsers] = useState(0)
  const [itemsPerPage, setItemsPerPage] = useState(10)
  const [confirmDialog, setConfirmDialog] = useState({
    isOpen: false,
    title: '',
    message: '',
    onConfirm: null
  })
  const [editingUser, setEditingUser] = useState(null)
  const [editRole, setEditRole] = useState('')
  const [updateSuccess, setUpdateSuccess] = useState(null)

  // Fetch users on component mount and when filters change
  useEffect(() => {
    fetchUsers()
  }, [page, itemsPerPage, roleFilter, sortBy, sortOrder, search])

  // Fetch users from API
  const fetchUsers = async () => {
    setLoading(true)
    setError(null)
    try {
      console.log('UserList: Fetching users...')

      // Use the user management API client
      const response = await userManagement.fetchUsers({
        page,
        limit: itemsPerPage,
        sortBy,
        sortOrder,
        search,
        role: roleFilter
      })

      if (!response || !response.users) {
        throw new Error('Failed to fetch users: Invalid response')
      }

      // Format the users data
      const formattedUsers = response.users.map(user => ({
        id: user.id,
        email: user.email,
        role: user.role || 'unknown',
        created_at: new Date(user.created_at).toLocaleDateString(),
        last_sign_in: user.last_sign_in_at
          ? new Date(user.last_sign_in_at).toLocaleDateString()
          : 'Never'
      }))

      setUsers(formattedUsers)
      setTotalUsers(response.total || 0)
      setTotalPages(response.pages || 1)
    } catch (err) {
      console.error('Error fetching users:', err)
      setError('Failed to load users. Please try again.')
    } finally {
      setLoading(false)
    }
  }

  // Handle sort change
  const handleSort = (column) => {
    if (sortBy === column) {
      setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')
    } else {
      setSortBy(column)
      setSortOrder('asc')
    }
  }

  // Handle role change
  const handleRoleChange = async (userId, newRole) => {
    try {
      console.log('UserList: Updating role for user:', userId, 'to', newRole)

      // Use the user management API client
      const data = await userManagement.updateUserRole(userId, newRole)

      // Update local state
      setUsers(users.map(user =>
        user.id === userId ? { ...user, role: newRole } : user
      ))

      setUpdateSuccess(`User role updated to ${newRole} successfully`)

      // Clear success message after 3 seconds
      setTimeout(() => {
        setUpdateSuccess(null)
      }, 3000)

      // Close edit mode
      setEditingUser(null)
    } catch (err) {
      console.error('Error updating user role:', err)
      setError(err.message || 'Failed to update user role')
    }
  }

  // Open edit role dialog
  const openEditRole = (user) => {
    setEditingUser(user)
    setEditRole(user.role)
  }

  // Cancel edit role
  const cancelEditRole = () => {
    setEditingUser(null)
    setEditRole('')
  }

  // Save edit role
  const saveEditRole = () => {
    if (editingUser && editRole) {
      handleRoleChange(editingUser.id, editRole)
    }
  }

  // Handle user deletion
  const handleDeleteUser = async (userId) => {
    try {
      console.log('UserList: Deleting user:', userId)

      // Use the user management API client
      const response = await userManagement.deleteUser(userId)

      // Remove user from local state
      setUsers(users.filter(user => user.id !== userId))
      setTotalUsers(totalUsers - 1)

      setUpdateSuccess('User deleted successfully')

      // Clear success message after 3 seconds
      setTimeout(() => {
        setUpdateSuccess(null)
      }, 3000)

    } catch (err) {
      console.error('Error deleting user:', err)
      setError(err.message || 'Failed to delete user')
    }
  }

  // Open delete confirmation dialog
  const openDeleteConfirmation = (user) => {
    setConfirmDialog({
      isOpen: true,
      title: 'Delete User',
      message: `Are you sure you want to delete user "${user.email}"? This action cannot be undone and will remove all associated data.`,
      onConfirm: () => handleDeleteUser(user.id)
    })
  }

  // Handle search input change
  const handleSearchChange = (e) => {
    setSearch(e.target.value)
    setPage(1) // Reset to first page on new search
  }

  // Handle role filter change
  const handleRoleFilterChange = (e) => {
    setRoleFilter(e.target.value)
    setPage(1) // Reset to first page on new filter
  }

  // Handle items per page change
  const handleItemsPerPageChange = (e) => {
    setItemsPerPage(Number(e.target.value))
    setPage(1) // Reset to first page on items per page change
  }

  // Render sort indicator
  const renderSortIndicator = (column) => {
    if (sortBy !== column) return null
    return (
      <span className={styles.sortIndicator}>
        {sortOrder === 'asc' ? '▲' : '▼'}
      </span>
    )
  }

  return (
    <div className={styles.userListContainer}>
      {error && (
        <div className={styles.error}>
          {error}
        </div>
      )}

      {updateSuccess && (
        <div className={styles.success}>
          {updateSuccess}
        </div>
      )}

      <div className={styles.filterContainer}>
        <div className={styles.searchInput}>
          <span className={styles.searchIcon}>
            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="18" height="18" fill="none" stroke="currentColor" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round">
              <circle cx="11" cy="11" r="8"></circle>
              <line x1="21" y1="21" x2="16.65" y2="16.65"></line>
            </svg>
          </span>
          <input
            type="text"
            placeholder="Search by email..."
            value={search}
            onChange={handleSearchChange}
          />
        </div>

        <div className={styles.filterOptions}>
          <select
            className={styles.filterSelect}
            value={roleFilter}
            onChange={handleRoleFilterChange}
          >
            <option value="all">All Roles</option>
            <option value="dev">Developer</option>
            <option value="admin">Admin</option>
            <option value="artist">Artist</option>
            <option value="braider">Braider</option>
            <option value="user">User</option>
          </select>

          <select
            className={styles.filterSelect}
            value={itemsPerPage}
            onChange={handleItemsPerPageChange}
          >
            <option value="5">5 per page</option>
            <option value="10">10 per page</option>
            <option value="25">25 per page</option>
            <option value="50">50 per page</option>
          </select>
        </div>
      </div>

      {loading ? (
        <div className={styles.loading}>Loading users...</div>
      ) : users.length === 0 ? (
        <div className={styles.noUsers}>
          No users found. Try adjusting your filters.
        </div>
      ) : (
        <>
          <div className={styles.tableContainer}>
            <table className={styles.userTable}>
              <thead>
                <tr>
                  <th onClick={() => handleSort('email')}>
                    Email {renderSortIndicator('email')}
                  </th>
                  <th onClick={() => handleSort('user_roles.role')}>
                    Role {renderSortIndicator('user_roles.role')}
                  </th>
                  <th onClick={() => handleSort('created_at')}>
                    Created {renderSortIndicator('created_at')}
                  </th>
                  <th onClick={() => handleSort('last_sign_in_at')}>
                    Last Sign In {renderSortIndicator('last_sign_in_at')}
                  </th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                {users.map(user => (
                  <tr key={user.id}>
                    <td>{user.email}</td>
                    <td>
                      {editingUser && editingUser.id === user.id ? (
                        <div className={styles.editRoleContainer}>
                          <select
                            value={editRole}
                            onChange={(e) => setEditRole(e.target.value)}
                            className={styles.editRoleSelect}
                          >
                            <option value="dev">Developer</option>
                            <option value="admin">Admin</option>
                            <option value="artist">Artist</option>
                            <option value="braider">Braider</option>
                            <option value="user">User</option>
                          </select>
                          <div className={styles.editRoleActions}>
                            <button
                              onClick={saveEditRole}
                              className={styles.saveButton}
                            >
                              Save
                            </button>
                            <button
                              onClick={cancelEditRole}
                              className={styles.cancelButton}
                            >
                              Cancel
                            </button>
                          </div>
                        </div>
                      ) : (
                        <span className={`${styles.roleTag} ${styles[user.role]}`}>
                          {user.role}
                        </span>
                      )}
                    </td>
                    <td>{user.created_at}</td>
                    <td>{user.last_sign_in}</td>
                    <td>
                      <div className={styles.actions}>
                        {editingUser && editingUser.id === user.id ? (
                          <button
                            onClick={cancelEditRole}
                            className={styles.cancelButton}
                          >
                            Cancel
                          </button>
                        ) : (
                          <>
                            <button
                              onClick={() => openEditRole(user)}
                              className={styles.editButton}
                            >
                              Edit Role
                            </button>

                            {/* Resend Welcome Email for Artist/Braider */}
                            {['artist', 'braider'].includes(user.role) && (
                              <ResendWelcomeEmailButton
                                user={user}
                                size="small"
                                variant="secondary"
                                onSuccess={(result) => {
                                  console.log('Welcome email resent:', result)
                                  // Optionally refresh user list or show additional feedback
                                }}
                              />
                            )}

                            {/* Token Management for Artist/Braider */}
                            {['artist', 'braider'].includes(user.role) && (
                              <button
                                onClick={() => setTokenManagement({ userId: user.id, userEmail: user.email })}
                                className={styles.tokenButton}
                                title="Manage Application Tokens"
                              >
                                🔑
                              </button>
                            )}

                            <button
                              onClick={() => openDeleteConfirmation(user)}
                              className={styles.deleteButton}
                              title="Delete User"
                            >
                              🗑️
                            </button>
                          </>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>

          <div className={styles.paginationContainer}>
            <div className={styles.paginationInfo}>
              Showing {(page - 1) * itemsPerPage + 1} to {Math.min(page * itemsPerPage, totalUsers)} of {totalUsers} users
            </div>

            <div className={styles.paginationButtons}>
              <button
                className={styles.paginationButton}
                onClick={() => setPage(1)}
                disabled={page === 1}
              >
                First
              </button>
              <button
                className={styles.paginationButton}
                onClick={() => setPage(page - 1)}
                disabled={page === 1}
              >
                Previous
              </button>

              {/* Page numbers */}
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                // Show pages around current page
                let pageNum = page;
                if (page > 2 && totalPages > 5) {
                  pageNum = Math.min(Math.max(page - 2, 1), totalPages - 4);
                } else {
                  pageNum = 1;
                }
                pageNum = pageNum + i;

                return (
                  <button
                    key={pageNum}
                    className={`${styles.paginationButton} ${page === pageNum ? styles.active : ''}`}
                    onClick={() => setPage(pageNum)}
                    disabled={page === pageNum}
                  >
                    {pageNum}
                  </button>
                );
              })}

              <button
                className={styles.paginationButton}
                onClick={() => setPage(page + 1)}
                disabled={page === totalPages}
              >
                Next
              </button>
              <button
                className={styles.paginationButton}
                onClick={() => setPage(totalPages)}
                disabled={page === totalPages}
              >
                Last
              </button>
            </div>
          </div>
        </>
      )}

      {/* Confirmation Dialog */}
      <ConfirmationDialog
        isOpen={confirmDialog.isOpen}
        title={confirmDialog.title}
        message={confirmDialog.message}
        onConfirm={() => {
          confirmDialog.onConfirm();
          setConfirmDialog({ ...confirmDialog, isOpen: false });
        }}
        onCancel={() => setConfirmDialog({ ...confirmDialog, isOpen: false })}
      />

      {/* Token Management Modal */}
      {tokenManagement && (
        <TokenManagement
          userId={tokenManagement.userId}
          userEmail={tokenManagement.userEmail}
          onClose={() => setTokenManagement(null)}
        />
      )}
    </div>
  )
}
